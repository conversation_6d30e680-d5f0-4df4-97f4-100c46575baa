/**
 * Test script for Reddit Puppeteer Scraper
 * Run with: node test-reddit-scraper.js
 */

const { scrapeRedditPosts, formatForN8n } = require('./reddit-puppeteer-scraper');

async function testScraper() {
  console.log('🚀 Testing Reddit Puppeteer Scraper...\n');
  
  try {
    // Test 1: Basic scraping
    console.log('📋 Test 1: Scraping r/AskReddit (top posts from today)');
    const posts = await scrapeRedditPosts('AskReddit', 'day', 3, true);
    
    console.log(`✅ Successfully scraped ${posts.length} posts\n`);
    
    // Display results
    posts.forEach((post, index) => {
      console.log(`📝 Post ${index + 1}:`);
      console.log(`   Title: ${post.title}`);
      console.log(`   Author: ${post.author}`);
      console.log(`   Upvotes: ${post.upvotes}`);
      console.log(`   URL: ${post.postUrl}`);
      console.log(`   Timestamp: ${post.timestamp}`);
      
      if (post.textContent) {
        const preview = post.textContent.length > 100 ? 
          post.textContent.substring(0, 100) + '...' : 
          post.textContent;
        console.log(`   Text: ${preview}`);
      }
      
      if (post.mediaUrl) {
        console.log(`   Media: ${post.mediaUrl}`);
      }
      
      console.log('');
    });
    
    // Test 2: n8n format
    console.log('📋 Test 2: Testing n8n format conversion');
    const n8nFormat = formatForN8n(posts);
    console.log(`✅ Converted to n8n format: ${n8nFormat.length} items`);
    console.log('Sample n8n item:', JSON.stringify(n8nFormat[0], null, 2));
    
    // Test 3: Different subreddit
    console.log('\n📋 Test 3: Testing different subreddit (r/technology)');
    const techPosts = await scrapeRedditPosts('technology', 'week', 2, true);
    console.log(`✅ Successfully scraped ${techPosts.length} posts from r/technology\n`);
    
    techPosts.forEach((post, index) => {
      console.log(`🔧 Tech Post ${index + 1}: ${post.title}`);
      console.log(`   Upvotes: ${post.upvotes}, Author: ${post.author}`);
    });
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  testScraper();
}
