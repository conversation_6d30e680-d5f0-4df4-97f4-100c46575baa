{"version": 3, "file": "QueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AAEtD,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAC,mBAAmB,EAAE,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAE3E,OAAO,EAAC,UAAU,EAAC,MAAM,aAAa,CAAC;AAEvC,OAAO,EAAC,uBAAuB,EAAC,MAAM,qBAAqB,CAAC;AAE5D,OAAO,EAAC,UAAU,EAAE,eAAe,EAAC,MAAM,qBAAqB,CAAC;AAChE,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAqBrC;;GAEG;AACH,MAAM,OAAO,YAAY;IAKvB,MAAM,KAAK,cAAc;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QAED,OAAO,CAAC,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAC9C,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;YACtC,MAAM,gBAAgB,GACpB,WAAW,CAAC,kBAAkB,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAChE,IAAI,KAAK,EAAE,MAAM,MAAM,IAAI,OAAO,EAAE;gBAClC,OAAO,MAAM,CAAC;aACf;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EACD;YACE,gBAAgB,EAAE,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC;SAC3D,CACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,KAAK,iBAAiB;QAC1B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAC9B;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;QAED,OAAO,CAAC,IAAI,CAAC,gBAAgB,GAAG,mBAAmB,CACjD,KAAK,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa;YAC5C,MAAM,aAAa,GAAkB,WAAW,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAClE,IAAI,MAAM,EAAE;gBACV,MAAM,MAAM,CAAC;aACd;QACH,CAAC,EACD;YACE,aAAa,EAAE,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;SACrD,CACF,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CACpB,OAA4B,EAC5B,QAAgB;QAEhB,MAAM,KAAK,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC;QAChD,MAAM,CAAC,KAAK,CAAC,CAAC;QACd,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CACzC,IAAI,CAAC,iBAAiB,EACtB,QAAQ,EACR,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACvB,OAAO,OAAO,CAAC,aAAa,CAAC;QAC/B,CAAC,CAAC,CACH,CAAC;QACF,KAAK,CAAC,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CACnB,OAA4B,EAC5B,QAAgB;QAEhB,MAAM,KAAK,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC;QAChD,MAAM,CAAC,KAAK,CAAC,CAAC;QACd,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CACzC,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACvB,OAAO,OAAO,CAAC,aAAa,CAAC;QAC/B,CAAC,CAAC,CACH,CAAC;QACF,IAAI,CAAC,CAAC,MAAM,YAAY,aAAa,CAAC,EAAE;YACtC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,cAA2C,EAC3C,QAAgB,EAChB,OAA+B;QAE/B,IAAI,KAAY,CAAC;QACjB,IAAI,OAAwC,CAAC;QAC7C,IAAI,CAAC,CAAC,cAAc,YAAY,aAAa,CAAC,EAAE;YAC9C,KAAK,GAAG,cAAc,CAAC;SACxB;aAAM;YACL,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;YAC7B,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;SAC3E;QAED,MAAM,EAAC,OAAO,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,EAAE,MAAM,EAAC,GAAG,OAAO,CAAC;QAEnE,IAAI;YACF,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;gBACnB,MAAM,IAAI,UAAU,CAAC,uCAAuC,CAAC,CAAC;aAC/D;YAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,eAAe,CAChE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;gBACtD,MAAM,aAAa,GAAG,aAAa,CAAC,cAAc,CAChD,KAAK,CACW,CAAC;gBACnB,MAAM,IAAI,GAAG,MAAM,aAAa,CAC9B,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,QAAQ,EAChB,QAAQ,EACR,aAAa,CACd,CAAC;gBACF,OAAO,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC,EACD;gBACE,OAAO,EAAE,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;gBAC/C,IAAI,EAAE,OAAO;gBACb,OAAO;gBACP,MAAM;aACP,EACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;YAC/B,CAAC,CAAC,EACF,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,EACtC,QAAQ,EACR,OAAO,EACP,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC5C,CAAC;YAEF,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;gBACvB,MAAM,IAAI,UAAU,CAAC,uCAAuC,CAAC,CAAC;aAC/D;YAED,IAAI,CAAC,CAAC,MAAM,YAAY,aAAa,CAAC,EAAE;gBACtC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACxD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;gBACvB,MAAM,KAAK,CAAC;aACb;YACD,KAAK,CAAC,OAAO,GAAG,0BAA0B,QAAQ,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC;YAChF,MAAM,KAAK,CAAC;SACb;gBAAS;YACR,IAAI,OAAO,EAAE;gBACX,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;aACzB;SACF;IACH,CAAC;CACF"}