{"name": "Simple Reddit Puppeteer Scraper", "nodes": [{"parameters": {"values": {"string": [{"name": "subreddit", "value": "AskReddit"}, {"name": "timeFilter", "value": "day"}], "number": [{"name": "maxPosts", "value": 5}]}}, "id": "set-params", "name": "Set Reddit Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"functionCode": "// Reddit Puppeteer Scraper for n8n\n// Copy the complete code from n8n-reddit-puppeteer-function.js\n\nconst puppeteer = require('puppeteer');\n\n// Get input parameters\nconst inputData = $input.first()?.json || {};\nconst subreddit = inputData.subreddit || 'AskReddit';\nconst timeFilter = inputData.timeFilter || 'day';\nconst maxPosts = inputData.maxPosts || 5;\n\nconsole.log(`Starting Reddit scrape: r/${subreddit}, filter: ${timeFilter}, posts: ${maxPosts}`);\n\nlet browser;\n\ntry {\n  browser = await puppeteer.launch({\n    headless: true,\n    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']\n  });\n\n  const page = await browser.newPage();\n  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');\n  await page.setViewport({ width: 1920, height: 1080 });\n  \n  const url = `https://www.reddit.com/r/${subreddit}/top/?t=${timeFilter}`;\n  await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });\n  \n  // Find posts using multiple selectors\n  let postsSelector = null;\n  const selectors = ['[data-testid=\"post-container\"]', 'shreddit-post', 'article'];\n  \n  for (const selector of selectors) {\n    try {\n      await page.waitForSelector(selector, { timeout: 5000 });\n      postsSelector = selector;\n      break;\n    } catch (e) {}\n  }\n  \n  if (!postsSelector) {\n    throw new Error('Could not find post containers');\n  }\n  \n  const posts = await page.evaluate((maxPosts, postsSelector) => {\n    const postElements = document.querySelectorAll(postsSelector);\n    const results = [];\n    \n    for (let i = 0; i < Math.min(postElements.length, maxPosts); i++) {\n      const postElement = postElements[i];\n      \n      try {\n        // Extract title\n        const titleElement = postElement.querySelector('h3[slot=\"title\"]') || \n                            postElement.querySelector('[slot=\"title\"]') ||\n                            postElement.querySelector('h3') ||\n                            postElement.querySelector('a[slot=\"full-post-link\"]');\n        const title = titleElement ? titleElement.textContent.trim() : 'No title';\n        \n        // Extract author\n        const authorElement = postElement.querySelector('[data-testid=\"post_author_link\"]') ||\n                             postElement.querySelector('a[href*=\"/user/\"]') ||\n                             postElement.querySelector('a[href*=\"/u/\"]');\n        const author = authorElement ? authorElement.textContent.replace(/^u\\//, '').trim() : 'Unknown';\n        \n        // Extract upvotes\n        const upvoteElement = postElement.querySelector('[slot=\"score\"]') ||\n                             postElement.querySelector('shreddit-score-subtext') ||\n                             postElement.querySelector('[data-testid=\"vote-arrows\"] span');\n        let upvotes = 0;\n        if (upvoteElement) {\n          const upvoteText = upvoteElement.textContent || '0';\n          const match = upvoteText.match(/(\\d+(?:\\.\\d+)?[kK]?)/);\n          if (match) {\n            let num = parseFloat(match[1]);\n            if (match[1].toLowerCase().includes('k')) {\n              num *= 1000;\n            }\n            upvotes = Math.floor(num);\n          }\n        }\n        \n        // Extract post URL\n        const linkElement = postElement.querySelector('a[href*=\"/comments/\"]') ||\n                           titleElement?.closest('a');\n        let postUrl = '';\n        if (linkElement) {\n          const href = linkElement.getAttribute('href');\n          postUrl = href.startsWith('http') ? href : `https://www.reddit.com${href}`;\n        }\n        \n        results.push({\n          title: title,\n          author: author,\n          upvotes: upvotes,\n          postUrl: postUrl,\n          subreddit: subreddit,\n          index: i + 1,\n          scrapedAt: new Date().toISOString()\n        });\n        \n      } catch (error) {\n        console.error(`Error extracting post ${i + 1}:`, error.message);\n      }\n    }\n    \n    return results;\n  }, maxPosts, postsSelector);\n  \n  await browser.close();\n  \n  console.log(`Successfully scraped ${posts.length} posts`);\n  return posts.map(post => ({ json: post }));\n  \n} catch (error) {\n  console.error('Scraping error:', error);\n  \n  if (browser) {\n    try { await browser.close(); } catch (e) {}\n  }\n  \n  return [{ json: { error: error.message, subreddit, timeFilter, maxPosts } }];\n}"}, "id": "puppeteer-scraper", "name": "Reddit Puppeteer <PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [420, 300]}, {"parameters": {"functionCode": "// Process scraped Reddit posts\nconst results = [];\n\nfor (const item of items) {\n  const post = item.json;\n  \n  // Skip errors\n  if (post.error) {\n    console.error('Scraping error:', post.error);\n    continue;\n  }\n  \n  // Apply filters (customize as needed)\n  if (!post.title || post.title === 'No title') continue;\n  if (post.title.length < 10) continue;\n  \n  // Clean and enhance the post data\n  const cleanPost = {\n    title: post.title,\n    author: post.author,\n    upvotes: post.upvotes || 0,\n    postUrl: post.postUrl,\n    subreddit: post.subreddit,\n    scrapedAt: post.scrapedAt,\n    // Add computed fields\n    titleLength: post.title.length,\n    isQuestion: post.title.includes('?'),\n    isSerious: post.title.toLowerCase().includes('[serious]')\n  };\n  \n  results.push(cleanPost);\n}\n\nconsole.log(`Processed ${results.length} valid posts from ${items.length} scraped`);\n\nreturn results.map(post => ({ json: post }));"}, "id": "process-posts", "name": "Process Posts", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [640, 300]}], "connections": {"Set Reddit Parameters": {"main": [[{"node": "Reddit Puppeteer <PERSON>", "type": "main", "index": 0}]]}, "Reddit Puppeteer Scraper": {"main": [[{"node": "Process Posts", "type": "main", "index": 0}]]}}}