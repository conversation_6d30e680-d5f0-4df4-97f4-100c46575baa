{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/common/HTTPRequest.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAiBA,OAAO,EAGL,YAAY,EACZ,WAAW,IAAI,eAAe,EAC9B,yBAAyB,EAIzB,YAAY,GACb,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AAKzC,OAAO,EAAC,UAAU,EAAE,QAAQ,EAAC,MAAM,WAAW,CAAC;AAE/C;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,eAAe;IA4B9C,IAAa,MAAM;QACjB,OAAO,uBAAA,IAAI,2BAAQ,CAAC;IACtB,CAAC;IAED,YACE,MAAkB,EAClB,KAAmB,EACnB,cAAkC,EAClC,iBAA0B,EAC1B,IAyBC,EACD,aAA4B;QAE5B,KAAK,EAAE,CAAC;;QA9DD,iBAAY,GAAkB,IAAI,CAAC;QACnC,cAAS,GAAwB,IAAI,CAAC;QACtC,qBAAgB,GAAG,KAAK,CAAC;QAGlC,sCAAoB;QACpB,mDAA8B;QAC9B,iDAA4B;QAC5B,2CAAuB,KAAK,EAAC;QAC7B,mCAAa;QACb,4CAA4B;QAE5B,sCAAgB;QAChB,wCAAmB;QACnB,+BAAmC,EAAE,EAAC;QACtC,qCAAqB;QACrB,wDAAoD;QACpD,0CAA0D,IAAI,EAAC;QAC/D,wCAAyD,IAAI,EAAC;QAC9D,gDAAsD;YACpD,MAAM,EAAE,yBAAyB,CAAC,IAAI;SACvC,EAAC;QACF,iDAAyD;QACzD,yCAAwC;QAwCtC,uBAAA,IAAI,uBAAW,MAAM,MAAA,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,uBAAA,IAAI,oCACF,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,MAAA,CAAC;QAC/D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,uBAAA,IAAI,kCAAsB,iBAAiB,MAAA,CAAC;QAC5C,uBAAA,IAAI,oBAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,MAAA,CAAC;QAC7B,uBAAA,IAAI,6BAAiB,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,WAAW,EAAkB,MAAA,CAAC;QAC1E,uBAAA,IAAI,uBAAW,IAAI,CAAC,OAAO,CAAC,MAAM,MAAA,CAAC;QACnC,uBAAA,IAAI,yBAAa,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAA,CAAC;QACvC,uBAAA,IAAI,sBAAU,KAAK,MAAA,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,uBAAA,IAAI,yCAA6B,EAAE,MAAA,CAAC;QACpC,uBAAA,IAAI,kCAAsB,EAAE,MAAA,CAAC;QAC7B,uBAAA,IAAI,0BAAc,IAAI,CAAC,SAAS,MAAA,CAAC;QAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/D,uBAAA,IAAI,4BAAS,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;SAC1C;IACH,CAAC;IAEQ,GAAG;QACV,OAAO,uBAAA,IAAI,wBAAK,CAAC;IACnB,CAAC;IAEQ,wBAAwB;QAC/B,MAAM,CAAC,uBAAA,IAAI,sCAAmB,EAAE,sCAAsC,CAAC,CAAC;QACxE,OAAO,uBAAA,IAAI,6CAA0B,CAAC;IACxC,CAAC;IAEQ,kBAAkB;QACzB,MAAM,CAAC,uBAAA,IAAI,sCAAmB,EAAE,sCAAsC,CAAC,CAAC;QACxE,OAAO,uBAAA,IAAI,uCAAoB,CAAC;IAClC,CAAC;IAEQ,gBAAgB;QACvB,MAAM,CAAC,uBAAA,IAAI,sCAAmB,EAAE,sCAAsC,CAAC,CAAC;QACxE,OAAO,uBAAA,IAAI,qCAAkB,CAAC;IAChC,CAAC;IAEQ,wBAAwB;QAC/B,IAAI,CAAC,uBAAA,IAAI,sCAAmB,EAAE;YAC5B,OAAO,EAAC,MAAM,EAAE,yBAAyB,CAAC,QAAQ,EAAC,CAAC;SACrD;QACD,IAAI,uBAAA,IAAI,wCAAqB,EAAE;YAC7B,OAAO,EAAC,MAAM,EAAE,yBAAyB,CAAC,cAAc,EAAC,CAAC;SAC3D;QACD,OAAO,EAAC,GAAG,uBAAA,IAAI,6CAA0B,EAAC,CAAC;IAC7C,CAAC;IAEQ,4BAA4B;QACnC,OAAO,uBAAA,IAAI,wCAAqB,CAAC;IACnC,CAAC;IAEQ,sBAAsB,CAC7B,cAAiD;QAEjD,uBAAA,IAAI,sCAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAEQ,KAAK,CAAC,qBAAqB;QAClC,MAAM,uBAAA,IAAI,sCAAmB,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,EAAE;YACrE,OAAO,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5C,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACtB,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjD,QAAQ,MAAM,EAAE;YACd,KAAK,OAAO;gBACV,OAAO,uBAAA,IAAI,kDAAO,MAAX,IAAI,EAAQ,uBAAA,IAAI,qCAAkB,CAAC,CAAC;YAC7C,KAAK,SAAS;gBACZ,IAAI,uBAAA,IAAI,uCAAoB,KAAK,IAAI,EAAE;oBACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;iBAC7D;gBACD,OAAO,uBAAA,IAAI,oDAAS,MAAb,IAAI,EAAU,uBAAA,IAAI,uCAAoB,CAAC,CAAC;YACjD,KAAK,UAAU;gBACb,OAAO,uBAAA,IAAI,qDAAU,MAAd,IAAI,EAAW,uBAAA,IAAI,6CAA0B,CAAC,CAAC;SACzD;IACH,CAAC;IAEQ,YAAY;QACnB,OAAO,uBAAA,IAAI,iCAAc,CAAC;IAC5B,CAAC;IAEQ,MAAM;QACb,OAAO,uBAAA,IAAI,2BAAQ,CAAC;IACtB,CAAC;IAEQ,QAAQ;QACf,OAAO,uBAAA,IAAI,6BAAU,CAAC;IACxB,CAAC;IAEQ,OAAO;QACd,OAAO,uBAAA,IAAI,4BAAS,CAAC;IACvB,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,KAAK;QACZ,OAAO,uBAAA,IAAI,0BAAO,CAAC;IACrB,CAAC;IAEQ,mBAAmB;QAC1B,OAAO,uBAAA,IAAI,wCAAqB,CAAC;IACnC,CAAC;IAEQ,SAAS;QAChB,OAAO,uBAAA,IAAI,8BAAW,CAAC;IACzB,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAEQ,OAAO;QACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,OAAO,IAAI,CAAC;SACb;QACD,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,YAAY;SAC7B,CAAC;IACJ,CAAC;IAEQ,KAAK,CAAC,QAAQ,CACrB,YAAsC,EAAE,EACxC,QAAiB;QAEjB,wDAAwD;QACxD,IAAI,uBAAA,IAAI,wBAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACjC,OAAO;SACR;QACD,MAAM,CAAC,uBAAA,IAAI,sCAAmB,EAAE,sCAAsC,CAAC,CAAC;QACxE,MAAM,CAAC,CAAC,uBAAA,IAAI,wCAAqB,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,uBAAA,IAAI,qDAAU,MAAd,IAAI,EAAW,SAAS,CAAC,CAAC;SAClC;QACD,uBAAA,IAAI,yCAA6B,SAAS,MAAA,CAAC;QAC3C,IACE,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,KAAK,SAAS;YACrD,QAAQ,GAAG,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,EAClD;YACA,uBAAA,IAAI,yCAA6B;gBAC/B,MAAM,EAAE,yBAAyB,CAAC,QAAQ;gBAC1C,QAAQ;aACT,MAAA,CAAC;YACF,OAAO;SACR;QACD,IAAI,QAAQ,KAAK,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,EAAE;YACxD,IACE,uBAAA,IAAI,6CAA0B,CAAC,MAAM,KAAK,OAAO;gBACjD,uBAAA,IAAI,6CAA0B,CAAC,MAAM,KAAK,SAAS,EACnD;gBACA,OAAO;aACR;YACD,uBAAA,IAAI,6CAA0B,CAAC,MAAM;gBACnC,yBAAyB,CAAC,QAAQ,CAAC;SACtC;QACD,OAAO;IACT,CAAC;IA6BQ,KAAK,CAAC,OAAO,CACpB,QAAqC,EACrC,QAAiB;QAEjB,qEAAqE;QACrE,IAAI,uBAAA,IAAI,wBAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACjC,OAAO;SACR;QACD,MAAM,CAAC,uBAAA,IAAI,sCAAmB,EAAE,sCAAsC,CAAC,CAAC;QACxE,MAAM,CAAC,CAAC,uBAAA,IAAI,wCAAqB,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,uBAAA,IAAI,oDAAS,MAAb,IAAI,EAAU,QAAQ,CAAC,CAAC;SAChC;QACD,uBAAA,IAAI,mCAAuB,QAAQ,MAAA,CAAC;QACpC,IACE,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,KAAK,SAAS;YACrD,QAAQ,GAAG,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,EAClD;YACA,uBAAA,IAAI,yCAA6B;gBAC/B,MAAM,EAAE,yBAAyB,CAAC,OAAO;gBACzC,QAAQ;aACT,MAAA,CAAC;YACF,OAAO;SACR;QACD,IAAI,QAAQ,KAAK,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,EAAE;YACxD,IAAI,uBAAA,IAAI,6CAA0B,CAAC,MAAM,KAAK,OAAO,EAAE;gBACrD,OAAO;aACR;YACD,uBAAA,IAAI,6CAA0B,CAAC,MAAM,GAAG,yBAAyB,CAAC,OAAO,CAAC;SAC3E;IACH,CAAC;IAmDQ,KAAK,CAAC,KAAK,CAClB,YAAuB,QAAQ,EAC/B,QAAiB;QAEjB,wDAAwD;QACxD,IAAI,uBAAA,IAAI,wBAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACjC,OAAO;SACR;QACD,MAAM,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,WAAW,EAAE,sBAAsB,GAAG,SAAS,CAAC,CAAC;QACxD,MAAM,CAAC,uBAAA,IAAI,sCAAmB,EAAE,sCAAsC,CAAC,CAAC;QACxE,MAAM,CAAC,CAAC,uBAAA,IAAI,wCAAqB,EAAE,6BAA6B,CAAC,CAAC;QAClE,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,uBAAA,IAAI,kDAAO,MAAX,IAAI,EAAQ,WAAW,CAAC,CAAC;SACjC;QACD,uBAAA,IAAI,iCAAqB,WAAW,MAAA,CAAC;QACrC,IACE,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,KAAK,SAAS;YACrD,QAAQ,IAAI,uBAAA,IAAI,6CAA0B,CAAC,QAAQ,EACnD;YACA,uBAAA,IAAI,yCAA6B;gBAC/B,MAAM,EAAE,yBAAyB,CAAC,KAAK;gBACvC,QAAQ;aACT,MAAA,CAAC;YACF,OAAO;SACR;IACH,CAAC;CAkBF;kwBAxJC,KAAK,gCAAW,YAAsC,EAAE;IACtD,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAC,GAAG,SAAS,CAAC;IACnD,uBAAA,IAAI,oCAAwB,IAAI,MAAA,CAAC;IAEjC,MAAM,oBAAoB,GAAG,QAAQ;QACnC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC1C,CAAC,CAAC,SAAS,CAAC;IAEd,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;QACtC,MAAM,IAAI,KAAK,CACb,yEAAyE,CAC1E,CAAC;KACH;IACD,MAAM,uBAAA,IAAI,2BAAQ;SACf,IAAI,CAAC,uBAAuB,EAAE;QAC7B,SAAS,EAAE,IAAI,CAAC,eAAe;QAC/B,GAAG;QACH,MAAM;QACN,QAAQ,EAAE,oBAAoB;QAC9B,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;KACrD,CAAC;SACD,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,uBAAA,IAAI,oCAAwB,KAAK,MAAA,CAAC;QAClC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;AACP,CAAC,yBAkCD,KAAK,+BAAU,QAAqC;IAClD,uBAAA,IAAI,oCAAwB,IAAI,MAAA,CAAC;IAEjC,MAAM,YAAY,GAChB,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC5B,CAAC,CAAE,QAAQ,CAAC,IAAe,IAAI,IAAI,CAAC;IAExC,MAAM,eAAe,GAAsC,EAAE,CAAC;IAC9D,IAAI,QAAQ,CAAC,OAAO,EAAE;QACpB,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAClD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEvC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC1D,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACf,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtB,CAAC,CAAC;gBACJ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACnB;KACF;IACD,IAAI,QAAQ,CAAC,WAAW,EAAE;QACxB,eAAe,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC;KACxD;IACD,IAAI,YAAY,IAAI,CAAC,CAAC,gBAAgB,IAAI,eAAe,CAAC,EAAE;QAC1D,eAAe,CAAC,gBAAgB,CAAC,GAAG,MAAM,CACxC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAChC,CAAC;KACH;IAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC;IACtC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;QACtC,MAAM,IAAI,KAAK,CACb,wEAAwE,CACzE,CAAC;KACH;IACD,MAAM,uBAAA,IAAI,2BAAQ;SACf,IAAI,CAAC,sBAAsB,EAAE;QAC5B,SAAS,EAAE,IAAI,CAAC,eAAe;QAC/B,YAAY,EAAE,MAAM;QACpB,cAAc,EAAE,YAAY,CAAC,MAAM,CAAC;QACpC,eAAe,EAAE,YAAY,CAAC,eAAe,CAAC;QAC9C,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;KACjE,CAAC;SACD,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,uBAAA,IAAI,oCAAwB,KAAK,MAAA,CAAC;QAClC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;AACP,CAAC,uBA8BD,KAAK,6BACH,WAAgD;IAEhD,uBAAA,IAAI,oCAAwB,IAAI,MAAA,CAAC;IACjC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;QACtC,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;KACH;IACD,MAAM,uBAAA,IAAI,2BAAQ;SACf,IAAI,CAAC,mBAAmB,EAAE;QACzB,SAAS,EAAE,IAAI,CAAC,eAAe;QAC/B,WAAW,EAAE,WAAW,IAAI,QAAQ;KACrC,CAAC;SACD,KAAK,CAAC,WAAW,CAAC,CAAC;AACxB,CAAC;AAGH,MAAM,YAAY,GAAoD;IACpE,OAAO,EAAE,SAAS;IAClB,YAAY,EAAE,cAAc;IAC5B,kBAAkB,EAAE,oBAAoB;IACxC,eAAe,EAAE,iBAAiB;IAClC,iBAAiB,EAAE,mBAAmB;IACtC,iBAAiB,EAAE,mBAAmB;IACtC,gBAAgB,EAAE,kBAAkB;IACpC,gBAAgB,EAAE,kBAAkB;IACpC,iBAAiB,EAAE,mBAAmB;IACtC,eAAe,EAAE,iBAAiB;IAClC,oBAAoB,EAAE,sBAAsB;IAC5C,eAAe,EAAE,iBAAiB;IAClC,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;CACR,CAAC;AAEX,KAAK,UAAU,WAAW,CAAC,KAAoB;IAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QACtD,MAAM,KAAK,CAAC;KACb;IACD,kEAAkE;IAClE,oEAAoE;IACpE,UAAU;IACV,UAAU,CAAC,KAAK,CAAC,CAAC;AACpB,CAAC"}