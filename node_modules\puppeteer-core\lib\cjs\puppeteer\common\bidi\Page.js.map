{"version": 3, "file": "Page.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/Page.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOH,+CAK2B;AAC3B,0DAAoD;AACpD,4DAA4E;AAI5E,wCAAuD;AAEvD,6CAAoD;AACpD,mDAA+C;AAE/C;;GAEG;AACH,MAAa,IAAK,SAAQ,cAAQ;IAQhC,YAAY,OAAgB;QAC1B,KAAK,EAAE,CAAC;;QARV,gCAAkB;QAClB,iCAAoB,IAAI,GAAG,CAAuB;YAChD,CAAC,gBAAgB,EAAE,uBAAA,IAAI,8CAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC,sBAAsB,EAAE,uBAAA,IAAI,qCAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC,kCAAkC,EAAE,uBAAA,IAAI,wCAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjE,CAAwD,EAAC;QAIxD,uBAAA,IAAI,iBAAY,OAAO,MAAA,CAAC;QAExB,uBAAA,IAAI,qBAAS,CAAC,UAAU;aACrB,IAAI,CAAC,mBAAmB,EAAE;YACzB,MAAM,EAAE;gBACN,GAAG,uBAAA,IAAI,8BAAkB,CAAC,IAAI,EAAE;aACa;YAC/C,QAAQ,EAAE,CAAC,uBAAA,IAAI,qBAAS,CAAC,EAAE,CAAC;SAC7B,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAClE,MAAM,KAAK,CAAC;aACb;QACH,CAAC,CAAC,CAAC;QAEL,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,uBAAA,IAAI,8BAAkB,EAAE;YACxD,uBAAA,IAAI,qBAAS,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;SACrC;IACH,CAAC;IA6DQ,KAAK,CAAC,KAAK;QAClB,MAAM,uBAAA,IAAI,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACzD,MAAM,EAAE,CAAC,GAAG,uBAAA,IAAI,8BAAkB,CAAC,IAAI,EAAE,CAAC;YAC1C,QAAQ,EAAE,CAAC,uBAAA,IAAI,qBAAS,CAAC,EAAE,CAAC;SAC7B,CAAC,CAAC;QAEH,MAAM,uBAAA,IAAI,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC3D,OAAO,EAAE,uBAAA,IAAI,qBAAS,CAAC,EAAE;SAC1B,CAAC,CAAC;QAEH,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,uBAAA,IAAI,8BAAkB,EAAE;YACxD,uBAAA,IAAI,qBAAS,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;SACtC;IACH,CAAC;IAEQ,KAAK,CAAC,cAAc,CAI3B,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,uBAAA,IAAI,qBAAS,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7D,CAAC;IAEQ,KAAK,CAAC,QAAQ,CAIrB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,uBAAA,IAAI,qBAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC;IAEQ,KAAK,CAAC,IAAI,CACjB,GAAW,EACX,OAGC;QAED,OAAO,uBAAA,IAAI,qBAAS,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAEQ,GAAG;QACV,OAAO,uBAAA,IAAI,qBAAS,CAAC,GAAG,EAAE,CAAC;IAC7B,CAAC;IAEQ,2BAA2B,CAAC,OAAe;QAClD,uBAAA,IAAI,qBAAS,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;IACtE,CAAC;IAEQ,iBAAiB,CAAC,OAAe;QACxC,uBAAA,IAAI,qBAAS,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEQ,KAAK,CAAC,UAAU,CACvB,IAAY,EACZ,UAA0B,EAAE;QAE5B,MAAM,uBAAA,IAAI,qBAAS,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YAC9B,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACpB,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aAClE;YACD,IAAI,QAAQ,CAAC,eAAe,EAAE;gBAC5B,MAAM,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC;aAC9C;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,GAAG,CAAC,UAAsB,EAAE;QACzC,MAAM,EAAC,IAAI,GAAG,SAAS,EAAC,GAAG,OAAO,CAAC;QACnC,MAAM,EACJ,eAAe,EAAE,UAAU,EAC3B,MAAM,EACN,SAAS,EACT,KAAK,EACL,MAAM,EACN,UAAU,EACV,KAAK,EACL,iBAAiB,EACjB,OAAO,GACR,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAA,yBAAe,EACpC,uBAAA,IAAI,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACrD,OAAO,EAAE,uBAAA,IAAI,qBAAS,CAAC,UAAU;YACjC,UAAU;YACV,MAAM;YACN,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU;YACjD,IAAI,EAAE;gBACJ,KAAK;gBACL,MAAM;aACP;YACD,UAAU,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;YAClC,KAAK;YACL,WAAW,EAAE,CAAC,iBAAiB;SAChC,CAAC,EACF,uBAAuB,EACvB,OAAO,CACR,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAElD,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEjD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEQ,KAAK,CAAC,eAAe,CAC5B,OAAgC;QAEhC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI;YACF,MAAM,EAAC,QAAQ,EAAC,GAAG,wDAAa,QAAQ,GAAC,CAAC;YAC1C,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC9B;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,SAAS,EAAE;gBAC9B,MAAM,IAAI,KAAK,CACb,uDAAuD,CACxD,CAAC;aACH;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAQQ,KAAK,CAAC,UAAU,CACvB,UAA6B,EAAE;QAE/B,MAAM,EAAC,IAAI,GAAG,SAAS,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAC,GAAG,OAAO,CAAC;QACtD,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACrE;QAED,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,uBAAA,IAAI,qBAAS,CAAC,UAAU,CAAC,IAAI,CAClD,mCAAmC,EACnC;YACE,OAAO,EAAE,uBAAA,IAAI,qBAAS,CAAC,UAAU;SAClC,CACF,CAAC;QAEF,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,OAAO,MAAM,CAAC,IAAI,CAAC;SACpB;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEjD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA3PD,oBA2PC;+JA7NkB,KAAwB;;IACvC,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAChC,OAAO,IAAA,0BAAa,EAAC,uBAAA,IAAI,qBAAS,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,IAAI;aACd,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACrB,MAAM,WAAW,GAAG,GAAG,CAAC,gBAAgB;gBACtC,CAAC,CAAC,8BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBAC/C,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,GAAG,KAAK,IAAI,WAAW,EAAE,CAAC;QACnC,CAAC,EAAE,EAAE,CAAC;aACL,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,IAAI,4CAEP,IAAI,kCAAc,CAChB,KAAK,CAAC,MAAa,EACnB,IAAI,EACJ,IAAI,EACJ,sBAAsB,CAAC,KAAK,CAAC,UAAU,CAAC,CACzC,CACF,CAAC;KACH;SAAM,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;QACtC,IAAI,OAAO,GAAG,MAAA,KAAK,CAAC,IAAI,mCAAI,EAAE,CAAC;QAE/B,IAAI,KAAK,CAAC,UAAU,EAAE;YACpB,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE;gBACnD,MAAM,QAAQ,GACZ,SAAS,CAAC,GAAG;oBACb,GAAG;oBACH,SAAS,CAAC,UAAU;oBACpB,GAAG;oBACH,SAAS,CAAC,YAAY,CAAC;gBACzB,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,IAAI,aAAa,CAAC;gBAC7D,OAAO,IAAI,YAAY,YAAY,KAAK,QAAQ,GAAG,CAAC;aACrD;SACF;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACjC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,sCAAsC;QAExD,IAAI,CAAC,IAAI,gDAA8B,KAAK,CAAC,CAAC;KAC/C;SAAM;QACL,IAAA,oBAAU,EACR,iCAAiC,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI,gBAAgB,KAAK,CAAC,KAAK,GAAG,CAChG,CAAC;KACH;AACH,CAAC,uCAEO,MAA2C;IACjD,IAAI,CAAC,IAAI,qCAAwB,CAAC;AACpC,CAAC,6CAEU,MAA2C;IACpD,IAAI,CAAC,IAAI,6DAAoC,CAAC;AAChD,CAAC;AAsKH,SAAS,iBAAiB,CACxB,KAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;AAClC,CAAC;AAED,SAAS,oBAAoB,CAC3B,KAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC;AACrC,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAmC;IAEnC,MAAM,mBAAmB,GAA6B,EAAE,CAAC;IACzD,IAAI,UAAU,EAAE;QACd,KAAK,MAAM,SAAS,IAAI,UAAU,CAAC,UAAU,EAAE;YAC7C,mBAAmB,CAAC,IAAI,CAAC;gBACvB,GAAG,EAAE,SAAS,CAAC,GAAG;gBAClB,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,YAAY,EAAE,SAAS,CAAC,YAAY;aACrC,CAAC,CAAC;SACJ;KACF;IACD,OAAO,mBAAmB,CAAC;AAC7B,CAAC"}