# Reddit Puppeteer Scraper for n8n

A robust Puppeteer-based Reddit scraping solution that replaces Reddit API/Pushshift API dependencies. This scraper uses a headless browser to extract top posts from any subreddit with comprehensive data extraction.

## 🚀 Features

- **Headless Browser Scraping**: Uses Puppeteer for reliable data extraction
- **No API Dependencies**: Works without Reddit API keys or rate limits
- **Comprehensive Data Extraction**: 
  - Post title, author, upvotes, timestamp
  - Post text content and media URLs
  - Post permalinks and subreddit info
- **n8n Ready**: Formatted output for seamless n8n integration
- **Flexible Parameters**: Configurable subreddit, time filters, and post limits
- **Error Handling**: Graceful error handling with detailed logging
- **Multiple Usage Options**: Standalone script, n8n Function node, or module import

## 📦 Installation

```bash
npm install puppeteer
```

## 🔧 Usage Options

### 1. n8n Function Node (Recommended)

Copy the code from `n8n-reddit-puppeteer-function.js` into an n8n Function node:

**Input Parameters** (via previous node or defaults):
```json
{
  "subreddit": "AskReddit",
  "timeFilter": "day",
  "maxPosts": 5
}
```

**Time Filter Options**: `hour`, `day`, `week`, `month`, `year`, `all`

### 2. n8n Workflow Import

Import `reddit-puppeteer-workflow.json` into n8n for a complete workflow with:
- Parameter setting node
- Puppeteer scraper function
- Post filtering and formatting

### 3. Standalone Script

```bash
# Basic usage
node reddit-puppeteer-scraper.js

# Custom parameters
node reddit-puppeteer-scraper.js AskReddit day 10
```

### 4. Module Import

```javascript
const { scrapeRedditPosts, formatForN8n } = require('./reddit-puppeteer-scraper');

async function example() {
  const posts = await scrapeRedditPosts('AskReddit', 'day', 5);
  const n8nFormat = formatForN8n(posts);
  return n8nFormat;
}
```

## 📊 Output Format

Each scraped post returns:

```json
{
  "title": "Post title text",
  "author": "username",
  "upvotes": 1234,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "textContent": "Post text content...",
  "mediaUrl": "https://i.redd.it/image.jpg",
  "postUrl": "https://www.reddit.com/r/subreddit/comments/...",
  "subreddit": "AskReddit",
  "index": 1,
  "scrapedAt": "2024-01-01T12:00:00.000Z"
}
```

## 🧪 Testing

Run the test script to verify functionality:

```bash
node test-reddit-scraper.js
```

This will:
- Test basic scraping from r/AskReddit
- Verify n8n format conversion
- Test different subreddit scraping
- Display sample results

## ⚙️ Configuration

### Browser Settings

The scraper uses optimized Puppeteer settings:
- Headless mode for server environments
- No sandbox for Docker/cloud compatibility
- Disabled GPU acceleration for stability
- Custom user agent to avoid detection

### Selectors

The scraper uses multiple fallback selectors for reliability:
- Post containers: `[data-testid="post-container"]`
- Titles: `h3[slot="title"]`, `[data-click-id="text"]`, `h3`
- Authors: <AUTHORS>
- Upvotes: `[data-testid="vote-arrows"]` elements
- Content: `[data-testid="post-content"]` elements

## 🔄 n8n Workflow Integration

### Basic Workflow Structure:

1. **Set Parameters Node**: Define subreddit, timeFilter, maxPosts
2. **Puppeteer Scraper Function**: Extract posts using headless browser
3. **Filter & Format Node**: Apply custom filters and formatting
4. **Next Processing Node**: Use scraped data for content generation, etc.

### Sample n8n Workflow Nodes:

```json
{
  "Set Parameters": {
    "subreddit": "nosleep",
    "timeFilter": "week", 
    "maxPosts": 10
  },
  "Puppeteer Scraper": "// Function code from n8n-reddit-puppeteer-function.js",
  "Filter Posts": "// Custom filtering logic",
  "Generate Content": "// Use posts for AI content generation"
}
```

## 🛠️ Troubleshooting

### Common Issues:

1. **Browser Launch Fails**:
   - Ensure Puppeteer is properly installed
   - Check system dependencies for headless Chrome
   - Try adding `--no-sandbox` flag

2. **Selectors Not Found**:
   - Reddit may have updated their HTML structure
   - Check browser console for current selectors
   - Update selectors in the script

3. **Timeout Errors**:
   - Increase timeout values for slow connections
   - Check if Reddit is accessible from your server
   - Verify network connectivity

4. **Empty Results**:
   - Check if subreddit exists and has posts
   - Verify time filter has posts in that period
   - Check console logs for extraction errors

### Debug Mode:

Set `headless: false` in the Puppeteer launch options to see the browser in action:

```javascript
browser = await puppeteer.launch({
  headless: false, // Set to true for production
  // ... other options
});
```

## 🔒 Best Practices

1. **Rate Limiting**: Add delays between requests for multiple subreddits
2. **Error Handling**: Always wrap in try-catch blocks
3. **Resource Cleanup**: Ensure browser.close() is called
4. **Monitoring**: Log scraping results and errors
5. **Respect Reddit**: Don't overload with excessive requests

## 📝 Customization

### Adding New Data Fields:

Modify the `page.evaluate()` function to extract additional data:

```javascript
// Add new field extraction
const commentCount = postElement.querySelector('[data-testid="comment-count"]')?.textContent || '0';

// Include in results
results.push({
  // ... existing fields
  commentCount: parseInt(commentCount) || 0
});
```

### Custom Filtering:

Modify the filter function to apply custom criteria:

```javascript
// Custom filters
if (post.upvotes < 500) continue; // Minimum upvotes
if (post.textContent.length < 200) continue; // Minimum text length
if (post.title.toLowerCase().includes('spam')) continue; // Content filtering
```

## 🆚 Comparison with API Methods

| Feature | Puppeteer Scraper | Reddit API | Pushshift API |
|---------|------------------|------------|---------------|
| Rate Limits | None | 60 req/min | Variable |
| Authentication | None | OAuth required | None |
| Real-time Data | Yes | Yes | Delayed |
| Reliability | High | Medium | Low (deprecated) |
| Setup Complexity | Low | High | Medium |
| Data Completeness | High | High | Medium |

## 📄 License

This project is provided as-is for educational and development purposes. Please respect Reddit's terms of service and use responsibly.
