/**
 * Reddit Scraper with File Saving Options
 * Demonstrates how to save scraped data to various file formats
 */

const fs = require('fs').promises;
const path = require('path');
const { scrapeRedditPosts } = require('./reddit-puppeteer-scraper');

// Create output directory if it doesn't exist
async function ensureOutputDir() {
  const outputDir = path.join(__dirname, 'scraped-data');
  try {
    await fs.mkdir(outputDir, { recursive: true });
    return outputDir;
  } catch (error) {
    console.error('Error creating output directory:', error);
    throw error;
  }
}

// Save data as JSON file
async function saveAsJSON(posts, subreddit, timeFilter) {
  const outputDir = await ensureOutputDir();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `reddit-${subreddit}-${timeFilter}-${timestamp}.json`;
  const filepath = path.join(outputDir, filename);
  
  const data = {
    metadata: {
      subreddit,
      timeFilter,
      scrapedAt: new Date().toISOString(),
      totalPosts: posts.length
    },
    posts
  };
  
  await fs.writeFile(filepath, JSON.stringify(data, null, 2));
  console.log(`✅ Saved JSON data to: ${filepath}`);
  return filepath;
}

// Save data as CSV file
async function saveAsCSV(posts, subreddit, timeFilter) {
  const outputDir = await ensureOutputDir();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `reddit-${subreddit}-${timeFilter}-${timestamp}.csv`;
  const filepath = path.join(outputDir, filename);
  
  // CSV headers
  const headers = ['title', 'author', 'upvotes', 'timestamp', 'postUrl', 'textContent'];
  let csvContent = headers.join(',') + '\n';
  
  // CSV rows
  posts.forEach(post => {
    const row = headers.map(header => {
      let value = post[header] || '';
      // Escape quotes and commas
      if (typeof value === 'string') {
        value = value.replace(/"/g, '""');
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          value = `"${value}"`;
        }
      }
      return value;
    });
    csvContent += row.join(',') + '\n';
  });
  
  await fs.writeFile(filepath, csvContent);
  console.log(`✅ Saved CSV data to: ${filepath}`);
  return filepath;
}

// Save data as Markdown file
async function saveAsMarkdown(posts, subreddit, timeFilter) {
  const outputDir = await ensureOutputDir();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `reddit-${subreddit}-${timeFilter}-${timestamp}.md`;
  const filepath = path.join(outputDir, filename);
  
  let markdown = `# Reddit Posts from r/${subreddit}\n\n`;
  markdown += `**Time Filter:** ${timeFilter}\n`;
  markdown += `**Scraped:** ${new Date().toISOString()}\n`;
  markdown += `**Total Posts:** ${posts.length}\n\n`;
  markdown += `---\n\n`;
  
  posts.forEach((post, index) => {
    markdown += `## ${index + 1}. ${post.title}\n\n`;
    markdown += `- **Author:** ${post.author}\n`;
    markdown += `- **Upvotes:** ${post.upvotes}\n`;
    markdown += `- **Posted:** ${post.timestamp}\n`;
    markdown += `- **URL:** [View Post](${post.postUrl})\n\n`;
    
    if (post.textContent) {
      markdown += `**Content:**\n${post.textContent}\n\n`;
    }
    
    if (post.mediaUrl) {
      markdown += `**Media:** [View Media](${post.mediaUrl})\n\n`;
    }
    
    markdown += `---\n\n`;
  });
  
  await fs.writeFile(filepath, markdown);
  console.log(`✅ Saved Markdown to: ${filepath}`);
  return filepath;
}

// Main function with file saving
async function scrapeAndSave(subreddit = 'AskReddit', timeFilter = 'day', maxPosts = 5, formats = ['json']) {
  try {
    console.log(`🚀 Scraping r/${subreddit} (${timeFilter}, ${maxPosts} posts)...`);
    
    // Scrape the data
    const posts = await scrapeRedditPosts(subreddit, timeFilter, maxPosts);
    console.log(`📊 Successfully scraped ${posts.length} posts`);
    
    // Save in requested formats
    const savedFiles = [];
    
    if (formats.includes('json')) {
      const jsonFile = await saveAsJSON(posts, subreddit, timeFilter);
      savedFiles.push(jsonFile);
    }
    
    if (formats.includes('csv')) {
      const csvFile = await saveAsCSV(posts, subreddit, timeFilter);
      savedFiles.push(csvFile);
    }
    
    if (formats.includes('markdown')) {
      const mdFile = await saveAsMarkdown(posts, subreddit, timeFilter);
      savedFiles.push(mdFile);
    }
    
    console.log(`\n📁 Files saved to: ${path.join(__dirname, 'scraped-data')}`);
    return { posts, savedFiles };
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

// Export functions
module.exports = {
  scrapeAndSave,
  saveAsJSON,
  saveAsCSV,
  saveAsMarkdown,
  ensureOutputDir
};

// Command line usage
if (require.main === module) {
  const args = process.argv.slice(2);
  const subreddit = args[0] || 'AskReddit';
  const timeFilter = args[1] || 'day';
  const maxPosts = parseInt(args[2]) || 5;
  const formats = args[3] ? args[3].split(',') : ['json', 'csv', 'markdown'];
  
  scrapeAndSave(subreddit, timeFilter, maxPosts, formats)
    .then(result => {
      console.log(`\n✅ Scraping completed! Saved ${result.savedFiles.length} files.`);
    })
    .catch(console.error);
}
