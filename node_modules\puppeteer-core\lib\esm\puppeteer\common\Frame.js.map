{"version": 3, "file": "Frame.js", "sourceRoot": "", "sources": ["../../../../src/common/Frame.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAOH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AASjD,OAAO,EAAC,0BAA0B,EAAC,MAAM,sBAAsB,CAAC;AAChE,OAAO,EACL,aAAa,GAGd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAC,UAAU,EAAE,eAAe,EAAC,MAAM,qBAAqB,CAAC;AAChE,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AACrC,OAAO,EAAC,gBAAgB,EAA0B,MAAM,uBAAuB,CAAC;AAEhF,OAAO,EAAC,gBAAgB,EAAC,MAAM,WAAW,CAAC;AA8E3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoDG;AACH,MAAM,OAAO,KAAK;IAsChB;;OAEG;IACH,YACE,YAA0B,EAC1B,OAAe,EACf,aAAiC,EACjC,MAAkB;QA5CpB,qBAAO,EAAE,EAAC;QACV,0BAAY,KAAK,EAAC;QAClB,gCAAqB;QAcrB;;WAEG;QACH,cAAS,GAAG,EAAE,CAAC;QAKf;;WAEG;QACH,uBAAkB,GAAG,KAAK,CAAC;QAC3B;;WAEG;QACH,qBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QAenC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,uBAAA,IAAI,cAAQ,EAAE,MAAA,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;QAC/B,uBAAA,IAAI,mBAAa,KAAK,MAAA,CAAC;QAEvB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,MAAkB;QAC7B,uBAAA,IAAI,iBAAW,MAAM,MAAA,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG;YACZ,CAAC,UAAU,CAAC,EAAE,IAAI,aAAa,CAAC,IAAI,CAAC;YACrC,CAAC,eAAe,CAAC,EAAE,IAAI,aAAa,CAAC,IAAI,CAAC;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,OAAO,uBAAA,IAAI,qBAAQ,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACH,KAAK,CAAC,IAAI,CACR,GAAW,EACX,UAKI,EAAE;QAEN,MAAM,EACJ,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,EACzE,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CACnE,gBAAgB,CACjB,EACD,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;QAEZ,IAAI,2BAA2B,GAAG,KAAK,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAClC,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;QACF,IAAI,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAC7B,QAAQ,CACN,uBAAA,IAAI,qBAAQ,EACZ,GAAG,EACH,OAAO,EACP,cAA8C,EAC9C,IAAI,CAAC,GAAG,CACT;YACD,OAAO,CAAC,2BAA2B,EAAE;SACtC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBACzB,OAAO,CAAC,2BAA2B,EAAE;gBACrC,2BAA2B;oBACzB,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE;oBACxC,CAAC,CAAC,OAAO,CAAC,6BAA6B,EAAE;aAC5C,CAAC,CAAC;SACJ;QAED,IAAI;YACF,IAAI,KAAK,EAAE;gBACT,MAAM,KAAK,CAAC;aACb;YACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;SAC3C;gBAAS;YACR,OAAO,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,KAAK,UAAU,QAAQ,CACrB,MAAkB,EAClB,GAAW,EACX,QAA4B,EAC5B,cAAwD,EACxD,OAAe;YAEf,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;oBAClD,GAAG;oBACH,QAAQ;oBACR,OAAO;oBACP,cAAc;iBACf,CAAC,CAAC;gBACH,2BAA2B,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAClD,IAAI,QAAQ,CAAC,SAAS,KAAK,qCAAqC,EAAE;oBAChE,OAAO,IAAI,CAAC;iBACb;gBACD,OAAO,QAAQ,CAAC,SAAS;oBACvB,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,OAAO,GAAG,EAAE,CAAC;oBAC9C,CAAC,CAAC,IAAI,CAAC;aACV;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;oBACtB,OAAO,KAAK,CAAC;iBACd;gBACD,MAAM,KAAK,CAAC;aACb;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,KAAK,CAAC,iBAAiB,CACrB,UAGI,EAAE;QAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;QACZ,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAClC,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;QACF,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAC/B,OAAO,CAAC,2BAA2B,EAAE;YACrC,OAAO,CAAC,6BAA6B,EAAE;YACvC,OAAO,CAAC,4BAA4B,EAAE;SACvC,CAAC,CAAC;QACH,IAAI;YACF,IAAI,KAAK,EAAE;gBACT,MAAM,KAAK,CAAC;aACb;YACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;SAC3C;gBAAS;YACR,OAAO,CAAC,OAAO,EAAE,CAAC;SACnB;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,qBAAQ,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,CAAC,CACL,QAAkB;QAElB,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,CACN,QAAkB;QAElB,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,KAAK,CAQT,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,MAAM,CAQV,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,EAAE,CAAC,UAAkB;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,KAAK,CAAC,eAAe,CACnB,QAAkB,EAClB,UAAkC,EAAE;QAEpC,MAAM,EAAC,eAAe,EAAE,YAAY,EAAC,GACnC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QACvC,OAAO,CAAC,MAAM,YAAY,CAAC,OAAO,CAChC,IAAI,EACJ,eAAe,EACf,OAAO,CACR,CAA4C,CAAC;IAChD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,YAAY,CAChB,KAAa,EACb,UAAkC,EAAE;QAEpC,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YAC1B,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;SACrB;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,eAAe,CAIb,YAA2B,EAC3B,UAAuC,EAAE,EACzC,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAC5C,YAAY,EACZ,OAAO,EACP,GAAG,IAAI,CACyC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,EAAE,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,UAAU,CACd,IAAY,EACZ,UAGI,EAAE;QAEN,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;OASG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,GAAG;QACD,OAAO,uBAAA,IAAI,kBAAK,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,uBAAA,IAAI,uBAAU,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAChB,OAAiC;QAEjC,IAAI,EAAC,OAAO,GAAG,EAAE,EAAE,IAAI,EAAC,GAAG,OAAO,CAAC;QACnC,MAAM,EAAC,IAAI,EAAC,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;SACH;QAED,IAAI,IAAI,EAAE;YACR,MAAM,EAAE,GAAG,MAAM,gBAAgB,EAAE,CAAC;YACpC,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC1C,OAAO,IAAI,iBAAiB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;SACvD;QAED,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,iBAAiB,CAAC;QAEjC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAC/C,KAAK,EAAE,EAAC,qBAAqB,EAAC,EAAE,EAAC,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAC,EAAE,EAAE;YAC1D,MAAM,OAAO,GAAG,qBAAqB,EAAQ,CAAC;YAC9C,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;YACtB,IAAI,GAAG,EAAE;gBACP,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;gBACjB,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,GAAG,EAAE;oBACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3B,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;gBACF,MAAM,CAAC,gBAAgB,CACrB,OAAO,EACP,KAAK,CAAC,EAAE;;oBACN,OAAO,CAAC,MAAM,CACZ,IAAI,KAAK,CAAC,MAAA,KAAK,CAAC,OAAO,mCAAI,uBAAuB,CAAC,CACpD,CAAC;gBACJ,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;aACH;iBAAM;gBACL,OAAO,CAAC,OAAO,EAAE,CAAC;aACnB;YACD,IAAI,EAAE,EAAE;gBACN,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;aAChB;YACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,OAAO,CAAC;YACd,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACvB,OAAO,OAAO,CAAC,aAAa,CAAC;QAC/B,CAAC,CAAC,EACF,EAAC,GAAG,OAAO,EAAE,IAAI,EAAE,OAAO,EAAC,CAC5B,CACF,CAAC;IACJ,CAAC;IAeD,KAAK,CAAC,WAAW,CACf,OAAgC;QAEhC,IAAI,EAAC,OAAO,GAAG,EAAE,EAAC,GAAG,OAAO,CAAC;QAC7B,MAAM,EAAC,IAAI,EAAC,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;SACH;QAED,IAAI,IAAI,EAAE;YACR,MAAM,EAAE,GAAG,MAAM,gBAAgB,EAAE,CAAC;YAEpC,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC1C,OAAO,IAAI,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;YAC7D,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;SAC3B;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,cAAc,CAC/C,KAAK,EAAE,EAAC,qBAAqB,EAAC,EAAE,EAAC,GAAG,EAAE,OAAO,EAAC,EAAE,EAAE;YAChD,MAAM,OAAO,GAAG,qBAAqB,EAAQ,CAAC;YAC9C,IAAI,OAA2C,CAAC;YAChD,IAAI,CAAC,GAAG,EAAE;gBACR,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC1C,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAQ,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACL,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC5C,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC;gBACxB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;gBAChB,OAAO,GAAG,IAAI,CAAC;aAChB;YACD,OAAO,CAAC,gBAAgB,CACtB,MAAM,EACN,GAAG,EAAE;gBACH,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;YACF,OAAO,CAAC,gBAAgB,CACtB,OAAO,EACP,KAAK,CAAC,EAAE;;gBACN,OAAO,CAAC,MAAM,CACZ,IAAI,KAAK,CACP,MAAC,KAAoB,CAAC,OAAO,mCAAI,sBAAsB,CACxD,CACF,CAAC;YACJ,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,EAAC,CACb,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,OAAO,CAAC;YACd,OAAO,OAAO,CAAC;QACjB,CAAC,EACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YACvB,OAAO,OAAO,CAAC,aAAa,CAAC;QAC/B,CAAC,CAAC,EACF,OAAO,CACR,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,KAAK,CACT,QAAgB,EAChB,UAAkC,EAAE;QAEpC,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,MAAM,CAAC,QAAgB,EAAE,GAAG,MAAgB;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,CAAC;IAClE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,GAAG,CAAC,QAAgB;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,KAAK,CAAC,IAAI,CACR,QAAgB,EAChB,IAAY,EACZ,OAAyB;QAEzB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,cAAc,CAAC,YAAoB;QACjC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3B,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,uBAAA,IAAI,qBAAQ,CAAC,CAAC;SACrE;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC;QAC7B,OAAO,WAAW,CAAC,2BAA2B,EAAE,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,mBAAmB,CACjB,UAA8B,EAAE;QAEhC,OAAO,IAAI,CAAC,2BAA2B,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,YAAiC;QAC1C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,uBAAA,IAAI,cAAQ,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,IAAI,EAAE,EAAE,MAAA,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,GAAW;QAClC,uBAAA,IAAI,cAAQ,GAAG,MAAA,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB,EAAE,IAAY;QAC9C,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SAC/B;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,uBAAA,IAAI,mBAAa,IAAI,MAAA,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;CACF"}