{"version": 3, "file": "PSelectorParser.js", "sourceRoot": "", "sources": ["../../../../src/injected/PSelectorParser.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAA6D;AAe7D,kBAAM,CAAC,YAAY,CAAC,GAAG,wBAAwB,CAAC;AAEhD,MAAM,aAAa,GAAG,WAAW,CAAC;AAClC,MAAM,OAAO,GAAG,CAAC,IAAY,EAAU,EAAE;IACvC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAClD,SAAS;aACV;YACD,OAAO,IAAI;iBACR,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;iBAChC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;gBAC9B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;SACN;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,SAAgB,eAAe,CAC7B,QAAgB;;IAEhB,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,MAAM,MAAM,GAAG,IAAA,oBAAQ,EAAC,QAAQ,CAAC,CAAC;IAClC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;KACxB;IACD,IAAI,gBAAgB,GAAsB,EAAE,CAAC;IAC7C,IAAI,eAAe,GAAqB,CAAC,gBAAgB,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAyB,CAAC,eAAe,CAAC,CAAC;IAC1D,MAAM,OAAO,GAAY,EAAE,CAAC;IAC5B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,YAAY;gBACf,QAAQ,KAAK,CAAC,OAAO,EAAE;oBACrB;wBACE,SAAS,GAAG,KAAK,CAAC;wBAClB,IAAI,OAAO,CAAC,MAAM,EAAE;4BAClB,gBAAgB,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAC,CAAC;4BAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;yBACnB;wBACD,gBAAgB,GAAG,EAAE,CAAC;wBACtB,eAAe,CAAC,IAAI,oCAAwB,CAAC;wBAC7C,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACvC,SAAS;oBACX;wBACE,SAAS,GAAG,KAAK,CAAC;wBAClB,IAAI,OAAO,CAAC,MAAM,EAAE;4BAClB,gBAAgB,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAC,CAAC;4BAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;yBACnB;wBACD,gBAAgB,GAAG,EAAE,CAAC;wBACtB,eAAe,CAAC,IAAI,gCAAmB,CAAC;wBACxC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACvC,SAAS;iBACZ;gBACD,MAAM;YACR,KAAK,gBAAgB;gBACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;oBACjC,MAAM;iBACP;gBACD,SAAS,GAAG,KAAK,CAAC;gBAClB,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,gBAAgB,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAC,CAAC;oBAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACnB;gBACD,gBAAgB,CAAC,IAAI,CAAC;oBACpB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACzB,KAAK,EAAE,OAAO,CAAC,MAAA,KAAK,CAAC,QAAQ,mCAAI,EAAE,CAAC;iBACrC,CAAC,CAAC;gBACH,SAAS;YACX,KAAK,OAAO;gBACV,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,gBAAgB,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAC,CAAC;oBAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACnB;gBACD,gBAAgB,GAAG,EAAE,CAAC;gBACtB,eAAe,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBACrC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAChC,SAAS;SACZ;QACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACrB;IACD,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,gBAAgB,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,OAAO,CAAC,CAAC,CAAC;KAC3C;IACD,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAChC,CAAC;AApED,0CAoEC"}