/**
 * Reddit Puppeteer Scraper - Usage Examples
 * Demonstrates different ways to use the scraper
 */

const { scrapeRedditPosts, formatForN8n, scrapeRedditPostsForN8n } = require('./reddit-puppeteer-scraper');

// Example 1: Basic scraping
async function example1_BasicScraping() {
  console.log('=== Example 1: Basic Scraping ===');
  
  try {
    const posts = await scrapeRedditPosts('AskReddit', 'day', 3);
    
    console.log(`Scraped ${posts.length} posts:`);
    posts.forEach((post, i) => {
      console.log(`${i + 1}. ${post.title}`);
      console.log(`   Author: ${post.author}, Upvotes: ${post.upvotes}`);
      console.log(`   URL: ${post.postUrl}\n`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 2: Different subreddits and time filters
async function example2_DifferentSubreddits() {
  console.log('=== Example 2: Different Subreddits ===');
  
  const configs = [
    { subreddit: 'technology', timeFilter: 'week', maxPosts: 2 },
    { subreddit: 'programming', timeFilter: 'day', maxPosts: 2 },
    { subreddit: 'science', timeFilter: 'month', maxPosts: 2 }
  ];
  
  for (const config of configs) {
    try {
      console.log(`\nScraping r/${config.subreddit} (${config.timeFilter})...`);
      const posts = await scrapeRedditPosts(config.subreddit, config.timeFilter, config.maxPosts);
      
      posts.forEach(post => {
        console.log(`- ${post.title} (${post.upvotes} upvotes)`);
      });
      
    } catch (error) {
      console.error(`Error scraping r/${config.subreddit}:`, error.message);
    }
  }
}

// Example 3: n8n format conversion
async function example3_N8nFormat() {
  console.log('\n=== Example 3: n8n Format ===');
  
  try {
    const posts = await scrapeRedditPosts('webdev', 'day', 2);
    const n8nFormat = formatForN8n(posts);
    
    console.log('n8n formatted data:');
    console.log(JSON.stringify(n8nFormat, null, 2));
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 4: Direct n8n function simulation
async function example4_N8nFunction() {
  console.log('\n=== Example 4: n8n Function Simulation ===');
  
  try {
    // Simulate n8n input
    const n8nResult = await scrapeRedditPostsForN8n('javascript', 'week', 2);
    
    console.log('Direct n8n function result:');
    n8nResult.forEach((item, i) => {
      const post = item.json;
      if (post.error) {
        console.log(`Error: ${post.error}`);
      } else {
        console.log(`${i + 1}. ${post.title}`);
        console.log(`   ${post.upvotes} upvotes by ${post.author}`);
      }
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 5: Content filtering
async function example5_ContentFiltering() {
  console.log('\n=== Example 5: Content Filtering ===');
  
  try {
    const posts = await scrapeRedditPosts('todayilearned', 'day', 5);
    
    // Filter posts with minimum upvotes and text content
    const filteredPosts = posts.filter(post => {
      return post.upvotes > 100 && 
             post.textContent && 
             post.textContent.length > 50;
    });
    
    console.log(`Filtered ${filteredPosts.length} posts from ${posts.length} total:`);
    filteredPosts.forEach(post => {
      console.log(`- ${post.title}`);
      console.log(`  ${post.upvotes} upvotes, ${post.textContent.length} chars`);
      console.log(`  Text preview: ${post.textContent.substring(0, 100)}...\n`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Example 6: Error handling
async function example6_ErrorHandling() {
  console.log('\n=== Example 6: Error Handling ===');
  
  try {
    // Try to scrape a non-existent subreddit
    const posts = await scrapeRedditPosts('nonexistentsubreddit123', 'day', 1);
    console.log('Unexpected success:', posts);
    
  } catch (error) {
    console.log('Expected error caught:', error.message);
  }
  
  // Using the n8n function which handles errors gracefully
  const n8nResult = await scrapeRedditPostsForN8n('anothernonexistentsubreddit', 'day', 1);
  console.log('n8n error handling result:', n8nResult[0].json);
}

// Run all examples
async function runAllExamples() {
  console.log('🚀 Reddit Puppeteer Scraper Examples\n');
  
  await example1_BasicScraping();
  await example2_DifferentSubreddits();
  await example3_N8nFormat();
  await example4_N8nFunction();
  await example5_ContentFiltering();
  await example6_ErrorHandling();
  
  console.log('\n✅ All examples completed!');
}

// Export for use in other scripts
module.exports = {
  example1_BasicScraping,
  example2_DifferentSubreddits,
  example3_N8nFormat,
  example4_N8nFunction,
  example5_ContentFiltering,
  example6_ErrorHandling,
  runAllExamples
};

// Run if called directly
if (require.main === module) {
  runAllExamples().catch(console.error);
}
