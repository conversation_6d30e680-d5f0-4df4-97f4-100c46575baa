{"version": 3, "file": "PQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/PQuerySelector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,uEAA+D;AAE/D,iEAA4D;AAC5D,qEAA8D;AAC9D,6DAQ8B;AAC9B,iEAA4D;AAC5D,uCAA4C;AAC5C,mEAA8D;AAE9D,MAAM,iBAAiB,GAAG,iBAAiB,CAAC;AAM5C,MAAM,eAAe,GAAG,CAAC,IAAU,EAAyB,EAAE;IAC5D,OAAO,kBAAkB,IAAI,IAAI,CAAC;AACpC,CAAC,CAAC;AAEF,MAAM,aAAc,SAAQ,KAAK;IAC/B,YAAY,QAAgB,EAAE,OAAe;QAC3C,KAAK,CAAC,GAAG,QAAQ,6BAA6B,OAAO,EAAE,CAAC,CAAC;IAC3D,CAAC;CACF;AAED,MAAM,YAAY;IAShB,YAAY,OAAa,EAAE,KAAa,EAAE,eAAiC;;QAR3E,sCAAe;QAEf,gDAAmC;QACnC,yCAAuC,EAAE,EAAC;QAC1C,iCAAuD,SAAS,EAAC;QAK/D,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1B,uBAAA,IAAI,uBAAU,KAAK,MAAA,CAAC;QACpB,uBAAA,IAAI,iCAAoB,eAAe,MAAA,CAAC;QACxC,uBAAA,IAAI,mDAAM,MAAV,IAAI,CAAQ,CAAC;IACf,CAAC;IAED,KAAK,CAAC,GAAG;QACP,IAAI,OAAO,uBAAA,IAAI,8BAAU,KAAK,QAAQ,EAAE;YACtC,QAAQ,uBAAA,IAAI,8BAAU,CAAC,SAAS,EAAE,EAAE;gBAClC,KAAK,QAAQ;oBACX,sEAAsE;oBACtE,iEAAiE;oBACjE,2DAA2D;oBAC3D,qEAAqE;oBACrE,mEAAmE;oBACnE,4DAA4D;oBAC5D,uBAAA,IAAI,mDAAM,MAAV,IAAI,CAAQ,CAAC;oBACb,MAAM;aACT;SACF;QAED,OAAO,uBAAA,IAAI,8BAAU,KAAK,SAAS,EAAE,uBAAA,IAAI,mDAAM,MAAV,IAAI,CAAQ,EAAE;YACjD,MAAM,QAAQ,GAAG,uBAAA,IAAI,8BAAU,CAAC;YAChC,MAAM,KAAK,GAAG,uBAAA,IAAI,2BAAO,CAAC;YAC1B,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,mEAAmE;gBACnE,oEAAoE;gBACpE,2DAA2D;gBAC3D,0BAA0B;gBAC1B,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;oBACtD,IAAI,CAAC,QAAQ,GAAG,wCAAiB,CAAC,OAAO,CACvC,IAAI,CAAC,QAAQ,EACb,KAAK,SAAS,CAAC,EAAE,OAAO;wBACtB,IAAI,eAAe,CAAC,OAAO,CAAC,EAAE;4BAC5B,KAAK,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;yBAC3C;oBACH,CAAC,CACF,CAAC;iBACH;qBAAM;oBACL,IAAI,CAAC,QAAQ,GAAG,wCAAiB,CAAC,OAAO,CACvC,IAAI,CAAC,QAAQ,EACb,KAAK,SAAS,CAAC,EAAE,OAAO;wBACtB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;4BAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;gCAC7B,OAAO;6BACR;4BACD,KAAK,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;4BAC1C,OAAO;yBACR;wBAED,IAAI,KAAK,GAAG,CAAC,CAAC;wBACd,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE;4BAClD,EAAE,KAAK,CAAC;4BACR,IAAI,KAAK,KAAK,OAAO,EAAE;gCACrB,MAAM;6BACP;yBACF;wBACD,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAC3C,qBAAqB,KAAK,IAAI,QAAQ,EAAE,CACzC,CAAC;oBACJ,CAAC,CACF,CAAC;iBACH;aACF;iBAAM;gBACL,IAAI,CAAC,QAAQ,GAAG,wCAAiB,CAAC,OAAO,CACvC,IAAI,CAAC,QAAQ,EACb,KAAK,SAAS,CAAC,EAAE,OAAO;oBACtB,QAAQ,QAAQ,CAAC,IAAI,EAAE;wBACrB,KAAK,MAAM;4BACT,KAAK,CAAC,CAAC,IAAA,2CAAoB,EAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;4BACrD,MAAM;wBACR,KAAK,OAAO;4BACV,KAAK,CAAC,CAAC,IAAA,6CAAqB,EAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;4BACtD,MAAM;wBACR,KAAK,MAAM;4BACT,KAAK,CAAC,CAAC,IAAA,2CAAoB,EAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;4BACrD,MAAM;wBACR;4BACE,MAAM,aAAa,GAAG,6CAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;4BAC9D,IAAI,CAAC,aAAa,EAAE;gCAClB,MAAM,IAAI,aAAa,CACrB,KAAK,EACL,0BAA0B,QAAQ,CAAC,IAAI,EAAE,CAC1C,CAAC;6BACH;4BACD,KAAK,CAAC,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;qBAClE;gBACH,CAAC,CACF,CAAC;aACH;SACF;IACH,CAAC;CA6BF;;IA1BG,IAAI,uBAAA,IAAI,sCAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;QACvC,uBAAA,IAAI,0BAAa,uBAAA,IAAI,sCAAkB,CAAC,KAAK,EAAE,MAAA,CAAC;QAChD,OAAO;KACR;IACD,IAAI,uBAAA,IAAI,qCAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;QACtC,uBAAA,IAAI,0BAAa,SAAS,MAAA,CAAC;QAC3B,OAAO;KACR;IACD,MAAM,QAAQ,GAAG,uBAAA,IAAI,qCAAiB,CAAC,KAAK,EAAE,CAAC;IAC/C,QAAQ,QAAQ,EAAE;QAChB,mCAAsB,CAAC,CAAC;YACtB,IAAI,CAAC,QAAQ,GAAG,wCAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAM,CAAC,CAAC;YACjE,uBAAA,IAAI,mDAAM,MAAV,IAAI,CAAQ,CAAC;YACb,MAAM;SACP;QACD,uCAA2B,CAAC,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,wCAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,mBAAS,CAAC,CAAC;YACpE,uBAAA,IAAI,mDAAM,MAAV,IAAI,CAAQ,CAAC;YACb,MAAM;SACP;QACD;YACE,uBAAA,IAAI,kCAAqB,QAA6B,MAAA,CAAC;YACvD,uBAAA,IAAI,mDAAM,MAAV,IAAI,CAAQ,CAAC;YACb,MAAM;KACT;AACH,CAAC;AAGH,MAAM,eAAe;IAArB;QACE,iCAAS,IAAI,OAAO,EAAkB,EAAC;IA4BzC,CAAC;IA1BC,SAAS,CAAC,IAAiB,EAAE,QAAkB,EAAE;QAC/C,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QACD,IAAI,IAAI,YAAY,UAAU,EAAE;YAC9B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SAClB;QAED,MAAM,WAAW,GAAG,uBAAA,IAAI,8BAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,WAAW,EAAE;YACf,OAAO,CAAC,GAAG,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC;SACnC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KACE,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,EACtC,WAAW,EACX,WAAW,GAAG,WAAW,CAAC,eAAe,EACzC;YACA,EAAE,KAAK,CAAC;SACT;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACvD,uBAAA,IAAI,8BAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC;IAC9B,CAAC;CACF;;AAED,MAAM,aAAa,GAAG,CAAC,CAAW,EAAE,CAAW,EAAc,EAAE;IAC7D,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAC7B,OAAO,CAAC,CAAC;KACV;IACD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACtC;IACD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,KAAK,SAAS,CAAC,EAAE,QAAiC;IAChE,MAAM,OAAO,GAAG,IAAI,GAAG,EAAQ,CAAC;IAChC,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,QAAQ,EAAE;QACpC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KACtB;IACD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;SACzB,GAAG,CAAC,MAAM,CAAC,EAAE;QACZ,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAU,CAAC;IACzD,CAAC,CAAC;SACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACrB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC;SACD,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;QAChB,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF;;;;GAIG;AACI,MAAM,iBAAiB,GAAG,UAC/B,IAAU,EACV,QAAgB;IAEhB,IAAI,SAA+B,CAAC;IACpC,IAAI,SAAkB,CAAC;IACvB,IAAI;QACF,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,IAAA,oCAAe,EAAC,QAAQ,CAAC,CAAC;KACpD;IAAC,OAAO,KAAK,EAAE;QACd,OAAQ,IAAiC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KACtE;IAED,IAAI,SAAS,EAAE;QACb,OAAQ,IAAiC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KACtE;IACD,sEAAsE;IACtE,4EAA4E;IAC5E,kDAAkD;IAClD,IACE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,EAAE,CAAC,CAAC;aACL;iBAAM;gBACL,CAAC,GAAG,CAAC,CAAC;aACP;YACD,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,EACF;QACA,MAAM,IAAI,aAAa,CACrB,QAAQ,EACR,8CAA8C,CAC/C,CAAC;KACH;IAED,OAAO,OAAO,CACZ,wCAAiB,CAAC,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE;QACnD,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QAC9D,KAAK,CAAC,GAAG,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC,QAAQ,CAAC;IACxB,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CAAC;AA5CW,QAAA,iBAAiB,qBA4C5B;AAEF;;;;GAIG;AACI,MAAM,cAAc,GAAG,KAAK,WACjC,IAAU,EACV,QAAgB;IAEhB,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,IAAA,yBAAiB,EAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;QAC7D,OAAO,OAAO,CAAC;KAChB;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AARW,QAAA,cAAc,kBAQzB"}