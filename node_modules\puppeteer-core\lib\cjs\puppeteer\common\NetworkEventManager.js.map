{"version": 3, "file": "NetworkEventManager.js", "sourceRoot": "", "sources": ["../../../../src/common/NetworkEventManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;AAkCH;;;;GAIG;AACH,MAAa,mBAAmB;IAAhC;QACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WA+BG;QACH,oDAAwB,IAAI,GAAG,EAG5B,EAAC;QACJ,gDAAoB,IAAI,GAAG,EAGxB,EAAC;QACJ,+CAAmB,IAAI,GAAG,EAAiC,EAAC;QAE5D;;;;;;;;WAQG;QACH,4DAAgC,IAAI,GAAG,EAGpC,EAAC;QACJ,qDAAyB,IAAI,GAAG,EAAsC,EAAC;QACvE,mDAAuB,IAAI,GAAG,EAAsC,EAAC;IA6GvE,CAAC;IA3GC,MAAM,CAAC,gBAAkC;QACvC,uBAAA,IAAI,iDAAsB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACpD,uBAAA,IAAI,6CAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAChD,uBAAA,IAAI,gDAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACnD,uBAAA,IAAI,kDAAuB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACrD,uBAAA,IAAI,yDAA8B,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC9D,CAAC;IAED,iBAAiB,CACf,gBAAkC;QAElC,IAAI,CAAC,uBAAA,IAAI,yDAA8B,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;YAC7D,uBAAA,IAAI,yDAA8B,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;SAC9D;QACD,OAAO,uBAAA,IAAI,yDAA8B,CAAC,GAAG,CAC3C,gBAAgB,CACoC,CAAC;IACzD,CAAC;IAEO,kBAAkB,CAAC,cAA8B;QACvD,IAAI,CAAC,uBAAA,IAAI,kDAAuB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YACpD,uBAAA,IAAI,kDAAuB,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;SACrD;QACD,OAAO,uBAAA,IAAI,kDAAuB,CAAC,GAAG,CAAC,cAAc,CAAqB,CAAC;IAC7E,CAAC;IAED,iBAAiB,CACf,cAA8B,EAC9B,YAA0B;QAE1B,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;IAED,sBAAsB,CACpB,cAA8B;QAE9B,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;IACzD,CAAC;IAED,qBAAqB;QACnB,OAAO,CAAC,GAAG,uBAAA,IAAI,4CAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE;YACvD,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC,MAAM,CAAC;IACZ,CAAC;IAED,sBAAsB,CACpB,gBAAkC,EAClC,KAA8C;QAE9C,uBAAA,IAAI,iDAAsB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED,oBAAoB,CAClB,gBAAkC;QAElC,OAAO,uBAAA,IAAI,iDAAsB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC1D,CAAC;IAED,uBAAuB,CAAC,gBAAkC;QACxD,uBAAA,IAAI,iDAAsB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB,CACd,gBAAkC;QAElC,OAAO,uBAAA,IAAI,6CAAkB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAED,mBAAmB,CAAC,gBAAkC;QACpD,uBAAA,IAAI,6CAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC;IAED,kBAAkB,CAChB,gBAAkC,EAClC,KAAwC;QAExC,uBAAA,IAAI,6CAAkB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,UAAU,CAAC,gBAAkC;QAC3C,OAAO,uBAAA,IAAI,4CAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IAED,YAAY,CAAC,gBAAkC,EAAE,OAAoB;QACnE,uBAAA,IAAI,4CAAiB,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,aAAa,CAAC,gBAAkC;QAC9C,uBAAA,IAAI,4CAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAED,mBAAmB,CACjB,gBAAkC;QAElC,OAAO,uBAAA,IAAI,gDAAqB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACzD,CAAC;IAED,eAAe,CACb,gBAAkC,EAClC,KAAuB;QAEvB,uBAAA,IAAI,gDAAqB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,sBAAsB,CAAC,gBAAkC;QACvD,uBAAA,IAAI,gDAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;CACF;AAtKD,kDAsKC"}