{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/api/HTTPRequest.ts"], "names": [], "mappings": "AAiEA;;;;GAIG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,CAAC,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAM,OAAO,WAAW;IA0BtB;;;;OAIG;IACH,IAAI,MAAM;QACR,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH;QArCA;;WAEG;QACH,eAAU,GAAG,EAAE,CAAC;QAKhB;;WAEG;QACH,iBAAY,GAAkB,IAAI,CAAC;QACnC;;WAEG;QACH,cAAS,GAAwB,IAAI,CAAC;QACtC;;WAEG;QACH,qBAAgB,GAAG,KAAK,CAAC;QACzB;;WAEG;QACH,mBAAc,GAAkB,EAAE,CAAC;IAcpB,CAAC;IAEhB;;OAEG;IACH,GAAG;QACD,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,wBAAwB;QACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;OAUG;IACH,wBAAwB;QACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,4BAA4B;QAC1B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAWD,sBAAsB;QACpB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,qBAAqB;QACzB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,QAAQ;QACN,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,aAAa;QACX,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,OAAO;QACL,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAmCD,KAAK,CAAC,QAAQ;QACZ,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAsCD,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAgBD,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,yBAOX;AAPD,WAAY,yBAAyB;IACnC,4CAAe,CAAA;IACf,gDAAmB,CAAA;IACnB,kDAAqB,CAAA;IACrB,kDAAqB,CAAA;IACrB,0CAAa,CAAA;IACb,+DAAkC,CAAA;AACpC,CAAC,EAPW,yBAAyB,KAAzB,yBAAyB,QAOpC;AAiCD;;GAEG;AACH,MAAM,UAAU,YAAY,CAC1B,OAA0C;IAE1C,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;QAC1B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;YAChC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAEtD,MAAM,CAAC,IAAI,CACT,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACpB,OAAO,EAAC,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAC,CAAC;YACnC,CAAC,CAAC,CACH,CAAC;SACH;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,YAAY,GAAwC;IAC/D,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,YAAY;IACnB,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,YAAY;IACnB,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,gBAAgB;IACvB,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,wBAAwB;IAC/B,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,oBAAoB;IAC3B,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,sBAAsB;IAC7B,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,WAAW;IAClB,KAAK,EAAE,kBAAkB;IACzB,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,mBAAmB;IAC1B,KAAK,EAAE,iCAAiC;IACxC,KAAK,EAAE,+BAA+B;IACtC,KAAK,EAAE,uBAAuB;IAC9B,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,aAAa;IACpB,KAAK,EAAE,qBAAqB;IAC5B,KAAK,EAAE,iBAAiB;IACxB,KAAK,EAAE,4BAA4B;IACnC,KAAK,EAAE,yBAAyB;IAChC,KAAK,EAAE,sBAAsB;IAC7B,KAAK,EAAE,eAAe;IACtB,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,iCAAiC;CAChC,CAAC"}