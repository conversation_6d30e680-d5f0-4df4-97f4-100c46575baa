/**
 * Debug script to inspect Reddit's HTML structure
 */

const puppeteer = require('puppeteer');

async function debugRedditStructure() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  await page.goto('https://www.reddit.com/r/AskReddit/top/?t=day', { 
    waitUntil: 'networkidle2' 
  });
  
  // Wait for posts to load
  await page.waitForSelector('shreddit-post', { timeout: 10000 });
  
  // Extract structure information
  const structure = await page.evaluate(() => {
    const posts = document.querySelectorAll('shreddit-post');
    const firstPost = posts[0];
    
    if (!firstPost) return { error: 'No posts found' };
    
    // Get all possible title elements
    const titleSelectors = [
      'h3[slot="title"]',
      '[data-click-id="text"]', 
      'h3',
      '[data-adclicklocation="title"]',
      'a[data-click-id="body"] h3',
      '.title a',
      '[data-testid="post-title"]',
      '[slot="title"]',
      'a[slot="full-post-link"]',
      'h1',
      'faceplate-tracker[source="post_title"] a',
      'a',
      'span'
    ];
    
    const titleResults = {};
    titleSelectors.forEach(selector => {
      const element = firstPost.querySelector(selector);
      titleResults[selector] = element ? {
        text: element.textContent?.trim(),
        innerHTML: element.innerHTML?.substring(0, 100),
        attributes: Array.from(element.attributes || []).map(attr => `${attr.name}="${attr.value}"`).join(' ')
      } : null;
    });
    
    return {
      postHTML: firstPost.outerHTML.substring(0, 1000),
      titleResults,
      allLinks: Array.from(firstPost.querySelectorAll('a')).map(a => ({
        text: a.textContent?.trim(),
        href: a.href,
        attributes: Array.from(a.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
      })).slice(0, 5)
    };
  });
  
  console.log('=== REDDIT STRUCTURE DEBUG ===');
  console.log('Post HTML (first 1000 chars):', structure.postHTML);
  console.log('\n=== TITLE SELECTOR RESULTS ===');
  Object.entries(structure.titleResults).forEach(([selector, result]) => {
    if (result) {
      console.log(`${selector}:`, result.text);
    }
  });
  
  console.log('\n=== ALL LINKS IN POST ===');
  structure.allLinks.forEach((link, i) => {
    console.log(`Link ${i + 1}: "${link.text}" -> ${link.href}`);
  });
  
  await browser.close();
}

debugRedditStructure().catch(console.error);
