{"name": "Reddit to YouTube Shorts Workflow", "description": "Complete workflow: Reddit scraping → TTS → Video creation → Upload", "nodes": [{"name": "1. Set Parameters", "type": "n8n-nodes-base.set", "parameters": {"values": {"string": [{"name": "subreddit", "value": "AskReddit"}, {"name": "timeFilter", "value": "day"}], "number": [{"name": "maxPosts", "value": 3}]}}}, {"name": "2. <PERSON><PERSON>", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "// Use the Reddit Puppeteer scraper code here"}}, {"name": "3. <PERSON><PERSON> Posts", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "// Filter posts by upvotes, length, engagement\nconst filteredPosts = items.filter(item => {\n  const post = item.json;\n  return post.upvotes > 100 && \n         post.title.length > 20 && \n         post.title.length < 200 &&\n         !post.title.includes('[NSFW]');\n});\n\nreturn filteredPosts.slice(0, 1); // Take best post"}}, {"name": "4. Generate TTS Audio", "type": "n8n-nodes-base.httpRequest", "parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/{{voice_id}}", "headers": {"xi-api-key": "{{$credentials.elevenLabs.apiKey}}", "Content-Type": "application/json"}, "body": {"text": "{{$json.title}}", "voice_settings": {"stability": 0.5, "similarity_boost": 0.5}}}}, {"name": "5. Save Audio File", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "// Save TTS audio to file system\nconst fs = require('fs');\nconst audioData = $binary.data;\nconst filename = `audio-${Date.now()}.mp3`;\nconst filepath = `/tmp/${filename}`;\n\nfs.writeFileSync(filepath, audioData);\n\nreturn [{\n  json: {\n    audioFile: filepath,\n    title: $json.title,\n    duration: 10 // estimate\n  }\n}];"}}, {"name": "6. Create Video with FFmpeg", "type": "n8n-nodes-base.executeCommand", "parameters": {"command": "ffmpeg", "arguments": ["-loop", "1", "-i", "/path/to/background.jpg", "-i", "{{$json.audioFile}}", "-c:v", "libx264", "-t", "{{$json.duration}}", "-pix_fmt", "yuv420p", "-vf", "scale=1080:1920", "-c:a", "aac", "-shortest", "/tmp/video-{{$json.timestamp}}.mp4"]}}, {"name": "7. Upload to YouTube", "type": "n8n-nodes-base.httpRequest", "parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos", "headers": {"Authorization": "Bearer {{$credentials.youtube.accessToken}}", "Content-Type": "application/json"}}}], "connections": {"1. Set Parameters": {"main": [["2. <PERSON><PERSON>"]]}, "2. Reddit Scraper": {"main": [["3. <PERSON><PERSON> Posts"]]}, "3. Filter Best Posts": {"main": [["4. Generate TTS Audio"]]}, "4. Generate TTS Audio": {"main": [["5. Save Audio File"]]}, "5. Save Audio File": {"main": [["6. Create Video with FFmpeg"]]}, "6. Create Video with FFmpeg": {"main": [["7. Upload to YouTube"]]}}, "notes": [{"content": "This is a conceptual workflow. You'll need:\n\n1. ElevenLabs API credentials for TTS\n2. YouTube API credentials\n3. FFmpeg installed on the system\n4. Background images/videos\n5. Proper file path management\n\nThe Reddit scraper provides the TEXT CONTENT that gets converted to video.", "position": [100, 500]}]}